= content_for :title do
  Pioneer - Edit #{@brand.try(:name) || 'Brand'}

.panel.panel-default
  .panel-body
    = render partial: 'details'
    #edit.mt-5
      = form_for @brand do |f|
        %fieldset
          .row
            .col-sm-6
              = f.text_field :login, class:"form-control"
              - if @brand.profile.present? && @brand.profile.id.present?
                %input{type: "hidden", name: "brand[profile_attributes][id]", value: @brand.profile.id}

              .manufacturer
                = f.label :manufacturer_id, 'Associated Manufacturer'
                %select{name: "brand[profile_attributes][manufacturer_id]", class:"form-control"}
                  - if @manufacturers.present?
                    - @manufacturers.each do |m|
                      %option{ value: m.id, selected: m.id == @brand.profile.try(:manufacturer_id)}= m.name
                  - else
                    %option{value: ""} No manufacturers available

              .shop
                = label_tag :shop_id, 'Owned Shop <i class="fa fa-question-circle" data-tooltip title="A shop can be owned by one brand only, if you assign a shop that already has another owner, that user will lose the ownership and will be granted to this brand."></i>'.html_safe
                %select{name: "brand[profile_attributes][shop_id]", class:"form-control"}
                  - ownable_shops = Pioneer::ShopManagementHelper.ownable_shops(@brand.try(:id), @network)
                  - if ownable_shops.present?
                    - current_shop_id = @brand.shop.try(:id)
                    - ownable_shops.each do |shop_name, shop_id|
                      %option{ value: shop_id, selected: shop_id == current_shop_id}= shop_name
                  - else
                    %option{value: ""} No shops available

              .sports
                = f.label :sports_ids, 'Sport Tags for Media Feeds <i class="fa fa-question-circle" data-tooltip title="These are the tags that will be added on all the content generated using the aggregated profiles functionality."></i>'.html_safe
                %select#profile_sports_ids{ name: 'brand[profile_attributes][sports_ids][]', multiple: true, class:"form-control"}
                  - sports_ids = @brand.profile.try(:sports_ids) || []
                  - if @sports.present?
                    - @sports.each do |sport|
                      %optgroup{ label: sport.name }
                        - if sport.children.present?
                          - sport.children.each do |sub_sport|
                            - selected = sports_ids.include?(sub_sport.id.to_s)
                            %option{ value: sub_sport.id, selected: selected }= sub_sport.name
                  - else
                    %option{value: ""} No sports available
              %br
              .brands-form-badges.panel.panel-defaul
                %fieldset
                  %legend Badges <i class="fa fa-question-circle" data-tooltip title="The badges are going to be shown on the Brand's Profile."></i>
                  - badges = t('brands.badges')
                  - if badges.present?
                    - badges.each do |key, badge|
                      .brands-form-badges-badge.col-sm-3
                        %label{ for: "profile_badges_#{key}" }= badge[:name]
                        %input{ type: 'checkbox', name: 'brand[profile_attributes][badges][]', id: "profile_badges_#{key}",value: key, checked: @brand.profile.try(:has_badge?, key) }
                  - else
                    %p No badges available
            .col-sm-6.panel.panel-default
              .panel-body
                %legend Managers <i class="fa fa-question-circle" data-tooltip title="These are the users that can post on behalf of the brand or manage the owned shop, or both."></i>
                - if @admins.present?
                  - @admins.each do |admin|
                    - if admin.present? && admin[:user].present?
                      .brand-manager.my-5
                        .row
                          = render partial: 'pioneer/users/user_ficha', locals: { user: admin[:user] }
                        .row.mt-3
                          .col-sm-1
                            %label.mt-2 Role:
                          .col-sm-11
                            - if admin[:user].present? && admin[:user].id.present?
                              %input{ type: 'hidden', name: 'managers[][user_id]', value: admin[:user].id }
                              %select.form-control{ name: 'managers[][role]', style: 'width:80%'}
                                - if @available_roles.present?
                                  - @available_roles.each do |role|
                                    %option{ value: role, selected: role == admin[:role] }= role
                                - else
                                  %option{value: "shop"} Shop
                - else
                  %p No managers available
                .row.mb-3
                  .col-sm-1
                    %label Add:
                  .col-sm-4
                    = select_tag('managers[][user_id]', nil, 'data-users-url': main_app.api_social_users_path, class:'form-control', id: 'managers_user_id')
                  .col-sm-4
                    %select.form-control{ name: 'managers[][role]', style:"width: 100%"}
                      - if @available_roles.present?
                        - @available_roles.each do |role|
                          %option{ value: role }= role
                      - else
                        %option{value: "shop"} Shop
                  .col-sm-3
                    %button.btn.btn-success{ type: 'submit' } Save

            .row
              .col-md-12{ style: "text-align: center" }
                %br
                %button.btn.btn-success Save

          = render partial: 'relationships'
