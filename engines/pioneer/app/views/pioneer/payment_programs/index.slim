= content_for :title do
  | Pioneer - Programas de pago


.panel.panel-default
  .panel-body
    .row
      .col-sm-12
        h3 = t('pioneer.payment_programs.title')
    br
    .row
      .col-sm-6
        = render partial: 'filter'
      .col-sm-6.text-right
        - if can?(:crud, 'Bines')
          = link_to( content_tag(:i, '', class: ['fa', 'fa-plus', 'pr-3']) + t('pioneer.payment_programs.create'), new_payment_program_url, class: 'btn btn-success' )
    br
    .row
      .col-sm-6
        = page_entries_info(@payment_programs, model: 'Programas')
      .col-sm-6.pagination
        = will_paginate(@payment_programs, class: 'right')
      br
      br

    .row
      .col-xs-12
        table.table
          thead
            th scope="col" = t('pioneer.payment_programs.name')
            th scope="col" = t('pioneer.payment_programs.installments')
            th scope="col"

          - @payment_programs.each do |payment_program|
            tr data-id= '#{payment_program.id}'
              td
                = payment_program.name
              td
                - installments_availables = payment_program.installments.uniq.sort_by(&:number).collect(&:full_description)
                - installments_availables.each do |installment|
                  span.ml-3 #{installment}
              td
                = link_to(content_tag(:i, '', class: ['fa', 'fa-edit', 'px-2']), edit_payment_program_path(payment_program), class: 'px-2', title:'Edit')
                = link_to(content_tag(:i, '', class: ['fa', 'fa-trash', 'px-2']), payment_program_path(payment_program), method: :delete, data: { confirm: t('pioneer.payment_programs.are_you_sure', name: payment_program.name ) },title: t('pioneer.payment_programs.delete'), class: 'px-2')
    .row
      .col-sm-12.pagination
        = will_paginate(@payment_programs, class: 'right')