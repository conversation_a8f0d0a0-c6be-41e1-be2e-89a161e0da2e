%li.py-2
  - if can?(:crud, "Category")
    - if category.products.empty? && !category.has_children?
      = link_to(content_tag(:i, '', class: ['fa', 'fa-trash', 'px-2']), category_path(category.id, store_id: params[:store_id]), action: :destroy, confirm: 'Are you sure?', method: :delete, class: 'px-2', title: 'Delete') # Modificado para incluir store_id
    - else
      = link_to(content_tag(:i, '', class: ['fa', 'fa-edit', 'px-2']), edit_category_path(category.id, store_id: params[:store_id]), class: 'px-2', title: 'Edit') # Modificado para incluir store_id

  = link_to_category category, params[:store_id] 
  %br
  - unless category.has_children?
    %ul
      %li
        %small
          = "#{category.products.joins(:shop).active.with_stock.count} #{t('pioneer.categories.products-stock')}"