- listable_type = order.listable_type.gsub('::', '-').downcase
tr.order-detail id="headingOrder#{order.id}"
  td.chevron.p-3
    a.p-2.cursor-pointer data-toggle="collapse" href="#collapseOrder#{order.id}" aria-expanded="true" aria-controls="collapseOrder#{order.id}" role="button" data-parent="#accordion" data-listable-id="#{order.listable_id}" data-listable-type="#{listable_type}" data-target="content-#{listable_type}-#{order.listable_id}"
      i.fa.fa-chevron-right.fs-16
  td.p-3.text-right = order.listable_id
  td.p-3 = order.created_at.strftime("%d-%m-%Y %H:%M")
  td.p-3.text-center = order.items_count
  td.p-3 = order.title
  td.p-3 = order.customer_full_name
  td.p-3
    img src="#{order.store.present? ? order.store.logo : '/assets/logos/logo-full.png'}"  style="height: 20px;"
  td.p-3
    - if order.total_without_points > 0 || order.total_points == 0
      = number_to_currency(order.total_without_points, precision: 2)
      br
    - if order.total_points > 0
      = "#{number_with_delimiter(order.total_points, :delimiter => ".")} puntos"

  td.p-3
    - if can?(:crud, 'Order')
      - dialog_id = "dialog-edit-#{order.listable_id}"
      = link_to '', "##{dialog_id}", 'data-toggle': 'modal', 'data-target': "##{dialog_id}", title: 'Edit Order', class: 'text-primary fa fa-edit', id: "link-#{order.id}"
      = render partial: 'order_edit', locals: { order: order, dialog_id: dialog_id }

  - if can?(:crud, 'Order')
    td.p-3
      - if listable_type == 'purchase'
        - if order.is_cancellable?
          - dialog_id = "dialog-purchase-cancel-#{order.listable_id}"
          = link_to '', "##{dialog_id}", 'data-toggle': 'modal', 'data-target': "##{dialog_id}", title: t('pioneer.orders.cancel-order'), class: 'text-danger cancel-order fs-16 fa fa-remove', id: "link-#{order.id}"
          = render partial: 'purchase_cancel_modal', locals: { order: order, dialog_id: dialog_id }
      - else
        - if order.is_cancellable?
          - dialog_id = "dialog-#{order.listable_id}"
          - shipment = order.shipments.detect { |s| s.shipped_or_delivered? }
          = link_to '', "##{dialog_id}", 'data-toggle': 'modal', 'data-target': "##{dialog_id}", title: t('pioneer.orders.cancel-order'), class: 'text-danger cancel-order fs-16 fa fa-remove', id: "link-#{order.id}"
          = render partial: 'cancel_modal', locals: { order: order, suborder: nil, dialog_id: dialog_id, shipment: shipment }
        - elsif !order.nil? && order.is_refundable?
          - dialog_id = "dialog-refund-#{order.listable_id}"
          = link_to '', "##{dialog_id}", 'data-toggle': 'modal', 'data-target': "##{dialog_id}", title: t('pioneer.orders.refund-order'), class: 'text-primary refund-order fs-16 fa fa-undo', id: "link-#{order.listable_id}"
          - if order.store.name == 'bancomacro'
            = render partial: 'macro_refund_modal', locals: {order: order, suborder: nil, dialog_id: dialog_id}
          - else
            = render partial: 'refund_modal', locals: {order: order, suborder: nil, dialog_id: dialog_id}

tr
  td.p-0.border-0 colspan="9"
    .collapse.m-3 id="content-#{listable_type}-#{order.listable_id}"
