= content_for :js do
  javascript:
      document.addEventListener('DOMContentLoaded', function() {
          const panelShopAccountNumberCollapse = document.getElementById('panelShopAccountNumberCollapse');
          const panelShopAccountNumberBody = document.getElementById('panelShopAccountNumberBody');
          panelShopAccountNumberCollapse.addEventListener('click', function() {
              panelShopAccountNumberBody.classList.toggle('hidden');
              panelShopAccountNumberCollapse.classList.toggle('btn-success');
              if(panelShopAccountNumberCollapse.classList.contains('btn-success')) {
                  panelShopAccountNumberCollapse.textContent = "+"
              }
              else {
                  panelShopAccountNumberCollapse.textContent = "X"
              }
          });
      });
div.panel.panel-default
  div.panel-heading
    div.panel-title.clearfix
      h4.pull-left
        = t('pioneer.shop.account_number_title')
      div.button-container.pull-right
        span.btn.btn-primary.btn-sm#panelShopAccountNumberCollapse
          = "X"
  div.panel-body#panelShopAccountNumberBody
    .row
      .col-md-4
        .form-group
          = f.text_field :account_number,
                  name: "mkp_shop[account_number]",
                  label: t('pioneer.shop.account_number'),
                  value: @shop.account_number,
                  class: 'form-control'
