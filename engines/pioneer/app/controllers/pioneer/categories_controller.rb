module Pioneer
  class CategoriesController < Pioneer::ApplicationController
    #load_and_authorize_resource class: Mkp::Category
    before_filter :normalize_blank_params, only: [:create, :update]
    after_filter :categories_stores_relation, only: [:create, :update]
    before_filter :have_permissions_to_read, :only => [:create, :edit, :index, :new, :show]
    before_filter :have_permissions_to_write, :only => [:create, :edit, :new, :update]
    before_filter :load_stores, only: [:new, :create, :edit, :update, :show, :index]
    before_filter :load_categories, only: [:index]

    # skip_before_filter :authenticate_admin_role!, only: [:edit, :update, :index]
    # before_filter :authenticate_store_role!, only: [:edit, :update, :index]
    layout 'bootstrap_layout'

    def create
      @parent_category = (Mkp::Category.find(params[:parent_id]) if params[:parent_id].present?)
      @category = Mkp::Category.new(categories_params) do |c|
        c.parent = @parent_category
        c.network = @network
      end
      @category.active = true if categories_params[:active].present? && categories_params[:active] === "1"

      if @category.save
        if @parent_category
          redirect_to category_path(@parent_category.id)
        else
          redirect_to :categories
        end
      else
        render :new
      end
    end

    def edit
      @category = Mkp::Category.find(params[:id])
      render :new
    end

    def update
      @category = Mkp::Category.find(params[:id])
      if @category.update_attributes(categories_params)
        redirect_to :categories
      else
        render :new
      end
    end

    def update_cucarda_stores
      if params[:store_id].present?
        @category = Mkp::Category.find(params[:id])
        @category.current_store_id = params[:store_id]

        @category.store_names ||= []
        @category.store_names.map!(&:to_s)

        store = Mkp::Store.find(params[:store_id])
        cucarda_active_value = categories_cucarda_params[:cucarda_active]

        if cucarda_active_value == 'true'
          @category.store_names << store.name
          @category.store_names.uniq!
        else
          @category.store_names -= [store.name]
        end

        if cucarda_active_value.present? && @category.update_attributes(categories_cucarda_params)
          redirect_to :categories
        else
          render :new
        end
      end

    end

    def destroy
      category = Mkp::Category.find(params[:id])
      category.destroy
      redirect_to :back
    end

    def index
      # Filter @categories by store
      if params[:store_id].present?
        store = Mkp::Store.find_by(id: params[:store_id])
        store.present? ? @categories = store.categories : params[:store_id] = nil
      end

      respond_to do |format|
        format.html do
          @categories = @categories.includes(products: :shop).active.by_network(@network).roots.order('name ASC')
          @categories_inactive = Mkp::Category.not_active.by_network(@network).roots.order('name ASC')
        end
        format.csv do
          @categories = Mkp::Category.where(network: @network).order('id ASC')
          send_data Pioneer::CategoryExporter.perform(@categories),
                    filename: 'goodpeople-categories.csv',
                    type: :csv,
                    disposition: :attachment,
                    status: :ok
        end
      end
    end

    def new
      @category = Mkp::Category.new(active: false)
      @parent_category = Mkp::Category.find(params[:parent_id]) if params[:parent_id].present?
    end

    def show
      @parent_category = Mkp::Category.find(params[:id])
      @store = Mkp::Store.find(params[:store_id]) if params[:store_id].present?
      @active_children_categories = @parent_category.children.active.order('name ASC')
      @inactive_children_categories = @parent_category.children.not_active.order('name ASC')
    end

    def delete_photo
      Social::Attachment::CoverPicture.find(params[:id]).destroy
      redirect_to edit_category_path(params[:category_id])
    end

    def normalize_blank_params
      params[:mkp_category][:length_unit] = nil if params[:mkp_category][:length_unit].blank?
      params[:mkp_category][:mass_unit] = nil if params[:mkp_category][:mass_unit].blank?
    end

    def categories_stores_relation
      if (store_ids = params['mkp_category']['store_ids']).present?
        stores = Mkp::Store.select { |s| store_ids.include?(s.id.to_s) }
        deletable_stores = @stores - stores

        @stores.each do |s|
          @category.stores.delete(s) if @category.stores.include?(s) && deletable_stores.include?(s)
          @category.stores << s if !@category.stores.include?(s) && !deletable_stores.include?(s)
        end
        @category.descendants.each do |c|
          @stores.each do |s|
            c.stores.delete(s) if c.stores.include?(s) && deletable_stores.include?(s)
            c.stores << s if !c.stores.include?(s) && !deletable_stores.include?(s)
          end
        end
      else
        @category.stores = []
        @category.descendants.each do |c|
          c.stores = []
          c.save
        end
      end

      @category.save

      # save installments
      if params[:mkp_category][:installments].present?
        params[:mkp_category][:installments].each do |key, value|
          category_store = @category.category_stores.find_by_store_id(key)
          category_store.update_attribute(:installments, value) if category_store.present?

          @category.descendants.each do |c|
            category_store = c.category_stores.find_by_store_id(key)
            category_store.update_attribute(:installments, value) if category_store.present?
          end
        end
      end

      # save commissions
      if params[:mkp_category][:commission].present?
        params[:mkp_category][:commission].each do |key, value|
          category_store = @category.category_stores.find_by_store_id(key)
          category_store.update_attribute(:commission, value) if category_store.present?
        end
      end

      if params[:mkp_category][:store_ids].present?
        params[:mkp_category][:store_ids].each do |store|
          if params[:mkp_category][:active_insurance]&.keys&.include?(store)
            category_store = @category.category_stores.find_by_store_id(store)
            category_store.update(active_insurance: true)
          else
            category_store = @category.category_stores.find_by_store_id(store)
            category_store.update(active_insurance: false)
          end
        end
      end

      # save insurance_coef
      if params[:mkp_category][:insurance_coef].present?
        params[:mkp_category][:insurance_coef].each do |key, value|
          category_store = @category.category_stores.find_by_store_id(key)
          category_store.update_attribute(:insurance_coef, value) if category_store.present?
        end
      end

      if params[:mkp_category][:insurance_start_date].present?
        params[:mkp_category][:insurance_start_date].each do |key, value|
          category_store = @category.category_stores.find_by_store_id(key)
          category_store.update_attribute(:insurance_start_date, value) if category_store.present?
        end
      end

      if params[:mkp_category][:insurance_end_date].present?
        params[:mkp_category][:insurance_end_date].each do |key, value|
          category_store = @category.category_stores.find_by_store_id(key)
          category_store.update_attribute(:insurance_end_date, value) if category_store.present?
        end
      end
    end

    def load_stores
      @stores = current_user.has_role?(:administrator) ? Mkp::Store.all : Mkp::Store.where(id: current_user.role.store.id)
      @stores = @stores.order(:name)
    end

    def load_categories
      @categories = current_user.has_role?(:administrator) ? Mkp::Category.all : current_user.role.store.categories
    end

    def get_products_by_categories
      @products = Mkp::Category.find(params[:category_id]).products.to_a
      respond_to do |format|
        format.json { render json: @products }
      end
    end

    def process_categories_csv
      file_path = params['categories']['file'].path
      ImportCategoriesWorker.perform_async(file_path)
      redirect_to categories_path
    end

    private


    def categories_params
      params.require(:mkp_category)
            .permit(:store_id, :name, :description, :pickup, :weight, :active, :mass_unit, :height, :length, :width, :length_unit, :visa_puntos_equivalence, :insurance_coef, :insurance_start_date, :insurance_end_date, :active_insurance, :cucarda_id, :cucarda_active)
            .except(:installments, :store_ids, :social_attachment_cover_picture, :store_id_pictures)
    end

    def categories_cucarda_params
      params.require(:mkp_category)
            .permit(:cucarda_id, :cucarda_active, :store_names)
    end

    def have_permissions_to_read
      redirect_to home_url, alert: 'Have not permissions' if cannot?(:read, 'Category')
    end

    def have_permissions_to_write
      redirect_to :categories, alert: 'Have not permissions' if cannot?(:crud, 'Category')
    end
  end
end


