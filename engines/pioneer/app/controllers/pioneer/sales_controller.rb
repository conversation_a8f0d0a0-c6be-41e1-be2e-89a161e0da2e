require 'sidekiq/api'

module Pioneer
  class SalesController < Pioneer::ApplicationController # rubocop:disable Metrics/ClassLength
    include Reporting::Streamable

    before_filter :permissions_to_write?
    before_filter :permissions_to_read?
    before_filter :set_filter_values, only: %i[index export]
    before_filter :find_orders, only: %i[index export]
    before_filter :find_order, only: %i[show cancel]

    layout 'bootstrap_layout'

    MAX_MONTHS_EXPORT = 3.freeze
    BNA_MI_MOTO_STORE_ID = "43"

    def index
      respond_to do |format|
        format.html do
          @orders = @orders.page(params[:page])
        end

        format.json { @orders = @orders.page(params[:page]) }
        format.csv { render_csv(OrderExporter, network: @network, orders: @orders.limit(1000)) }
      end
    end

    def show
      render layout: false
    end

    def cancel
      service = "Gateways::Refunds::#{@order.gateway.camelize}".constantize.new(@order)
      service.perform

      render layout: false, json: { status: 200, message: service.status, success: service.valid }
    end

    def refund
      service = "Gateways::Refunds::#{@order.gateway.camelize}".constantize.new(@order)
      service.perform

      render layout: false, json: { status: 200, message: service.status, success: service.valid }
    end

    def eval_between_dates(params)
      if params[:created_at_gte].present? && params[:created_at_lt].present?
        return { from: params[:created_at_gte],  to: params[:created_at_lt] }
      end

      nil
    end

    def export # rubocop:disable Metrics/MethodLength, Metrics/AbcSize
      begin
        dates = eval_between_dates(params)
        store = @with_store_id.presence

        if check_needed_params(dates: dates, store: store)
          jobs_count = Sidekiq::ScheduledSet.new.count { |job| job.klass == 'ExportSalesWorker' }
          next_time = [0, jobs_count].max * 4
          export = current_user.role.store.exports.orders.create
          ExportSalesWorker.perform_in(next_time.minutes, { order_created_from: dates[:from],
                                                              order_created_to: dates[:to],
                                                              store: store.to_i,
                                                              export_id: export.id })
          flash[:success] = "Tu export estará disponible en unos minutos. El link
                            para descargar el export aparecerá en el listado de exports"
        else
          flash[:error] = error_parser(params)
        end
      rescue StandardError => e
        Rails.logger.info("Error desconocido al exportar ordenes Pioneer: #{e}")
      end
      redirect_to sales_path
    end

    def tracking
      label = Mkp::ShipmentLabel.find_by id: params[:label_id]
      if label
        render json: build_tracking(label), status: 200
      else
        render json: { error: 'Label not found' }, status: 404
      end
    end

    private

    def set_months_ago
      months_quantity = 6
      months_quantity.months.ago
    end

    def created_date_current(date = nil, gte = nil)
      return date.present? ? Time.zone.parse(date).to_datetime.beginning_of_day : set_months_ago.beginning_of_month if gte

      date.present? ? Time.zone.parse(date).to_datetime.end_of_day : Time.current.end_of_month
    end

    def find_orders # rubocop:disable Metrics/AbcSize, Metrics/PerceivedComplexity, Metrics/CyclomaticComplexity, Metrics/MethodLength
      @stores = current_user.has_role?(:administrator) ? Mkp::Store.all : [current_user.role.store]
      created_at_gte = created_date_current(params[:created_at_gte], true)
      created_at_lt = created_date_current(params[:created_at_lt])

      # @orders = Mkp::Order.includes(:items, :customer, :store, :shipments).where(store_id: @stores.map(&:id))

      @orders = SaleItem.includes(:customer, listable: [:items, :shipments, :store, :customer]).where(store_id: @stores.map(&:id))
      @orders = @orders.created_at_gte(created_at_gte) if created_at_gte.present?
      @orders = @orders.created_at_lt(created_at_lt) if created_at_lt.present?
      if params[:payment_created_at_gte].present?
        @orders = @orders.with_payment_created_at_gte(params[:payment_created_at_gte])
      end
      if params[:payment_created_at_lt].present?
        @orders = @orders.with_payment_created_at_lt(params[:payment_created_at_lt])
      end
      if params[:with_payment_status].present?
        @orders = @orders.with_payment_status(params[:with_payment_status])
      end
      if params[:with_shipment_status].present?
        @orders = @orders.with_shipment_status(params[:with_shipment_status])
      end
      @orders = @orders.with_store_id(params[:with_store_id]) if params[:with_store_id].present?
      @orders = @orders.search_query(params[:search_query]) if params[:search_query].present?
      @orders = @orders.with_payment_status_not_pending_or_expired unless @with_store_id == BNA_MI_MOTO_STORE_ID
      @orders = @orders&.order('created_at desc')
    end

    def find_order
      listable_type = params[:type].casecmp('purchase').zero? ? 'Purchase' : 'Mkp::Order'
      @order = SaleItem.find_by(listable_id: params[:id], listable_type: listable_type)
    end

    def permissions_to_write?
      return true if current_user && can?(:crud, 'Order')
      return false if can?(:read, 'Order')
      redirect_to home_url, alert: 'Have not permissions'
    end

    def permissions_to_read?
      return true if current_user && can?(:read, 'Order')
      redirect_to home_url, alert: 'Have not permissions'
    end

    def parser_dates(date)
      Time.zone.parse(date).strftime('%Y-%m-%d')
    end

    def set_filter_values # rubocop:disable Metrics/AbcSize, Metrics/CyclomaticComplexity, Metrics/MethodLength, Metrics/PerceivedComplexity
      @created_at_gte = parser_dates(params[:created_at_gte]) if params[:created_at_gte].present?
      @created_at_lt = parser_dates(params[:created_at_lt]) if params[:created_at_lt].present?
      if params[:payment_created_at_gte].present?
        @payment_created_at_gte = parser_dates(params[:payment_created_at_gte])
      end
      if params[:payment_created_at_lt].present?
        @payment_created_at_lt = parser_dates(params[:payment_created_at_lt])
      end
      @with_store_id = params[:with_store_id] if params[:with_store_id].present?
      @with_payment_status = params[:with_payment_status] if params[:with_payment_status].present?
      if params[:with_shipment_status].present?
        @with_shipment_status = params[:with_shipment_status]
      end
      @search_query = params[:search_query] if params[:search_query].present?
    end

    def build_tracking(label) # rubocop:disable Metrics/AbcSize, Metrics/MethodLength
      result = {
        courier: label.courier.capitalize,
        icon: label.icon,
        status: label.shipment.status,
        tracking: label.tracking_number,
        estimated_delivery_date: estimated_delivery_date(label),
        events: []
      }
      label.shipment.status_changes.order(:created_at).each do |status_change|
        result[:events] << {
          description: I18n.t("pioneer.orders.#{status_change.status}"),
          date: status_change.created_at.strftime('%d-%m-%Y %H:%M')
        }
      end

      result
    end

    def estimated_delivery_date(label)
      return nil if label.estimated_delivery_date.blank?

      label.estimated_delivery_date.strftime('%d-%m-%Y %H:%M')
    end

    def months_between_dates(date_from, date_to)
      from = Date.parse(date_from)
      to = Date.parse(date_to)
      (to.year * 12 + to.month) - (from.year * 12 + from.month)
    end

    def months_between_dates?(dates)
      dates.present? ? (months_between_dates(dates[:from], dates[:to]) <= MAX_MONTHS_EXPORT) : false
    end

    def check_needed_params(dates:, store:)
      months_between_dates?(dates) && store.present?
    end

    def error_parser(params)
      if params[:with_store_id].empty?
        'Se debe indicar una tienda para realizar el export.'
      elsif params[:created_at_gte].nil? || params[:created_at_lt].nil?
        "El rango de fecha para exportar órdenes está limitado a
        #{MAX_MONTHS_EXPORT} mes(es). Por favor, intentá de nuevo."
      else
        'Error desconocido en los parámetros.'
      end
    end
  end
end
