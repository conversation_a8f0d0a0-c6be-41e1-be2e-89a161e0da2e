class ExportReservationsWorker < ExportSalesWorker

  def get_orders_info(date_from, date_to, store)
    from = Date.parse(date_from).beginning_of_day
    to = Date.parse(date_to).end_of_day
    Mkp::Order
    .includes(:store)
    .includes(:customer)
    .includes(suborders: [{ shop: [:setting] },
                          { items: [{ product: [:category, shop: [:setting]] },
                                    { variant: [product: [:shop, :category]] }]}])
    .where(created_at: from..to, store_id: store)
    .includes(:invoices)
    .to_a
  end

  # Replace all the methods that the reservation orders cannot fullfil
  # Reservations dont have payments and shippings
  
  private

  def get_paid_shipping_price(item)
    '-'
  end

  def get_subsidized_shipping(suborder, item, order)
    '-'
  end

  def get_payment_external_id(payment)
    '-'
  end

  def item_payment_external_ids(payment)
    '-'
  end

  def item_cc_bank(payment)
    '-'
  end

  def payment_method(payment)
    '-'
  end

  def item_payment_methods(payment)
    '-'
  end

  def payment_total(payment)
    '-'
  end

  def distributed?(gateway)
    '-'
  end

  def visa_points?(gateway)
    '-'
  end

  def suborder_payment_total(suborder, gateway)
    '-'
  end

  def get_credit_card(payment)
    '-'
  end

  def get_cc_number(payment)
    '-'
  end

  def get_installments(payment)
    '-'
  end

  def card_last_four_digits(payment)
    '-'
  end

  def gateway(payment)
    '-'
  end

  def get_credit_notes_ids(credit_notes)
    '-'
  end

  def item_gateways(payment)
    '-'
  end

  def get_identification(payment, key)
    '-'
  end

  #SHIPMENT

  def get_shipment_status(suborder)
    '-'
  end

  def get_shipment_kind(suborder)
    '-'
  end

  def get_shipment_status_updated_at(suborder)
    '-'
  end

end

