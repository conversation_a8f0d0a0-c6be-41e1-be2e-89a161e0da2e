require 'sidekiq/web'
require 'sidekiq_status/web'
Pioneer::Engine.routes.draw do
  resources :blacklists
  root to: 'application#home', as: :home

  match 'logout' => 'sessions#destroy', as: :logout, via: %i[get post options]

  resources :sessions
  resources :password_resets, only: %i[new create edit update]

  namespace :ajax do
    get :users,    to: 'users#all'
    get :brands,   to: 'brands#all'
    get :variants, to: 'variants#all'
    get :products, to: 'products#index'

    resources :pictures, only: %i[create destroy]
    resources :components, except: %i[edit show index]

    resources :suggestions, only: [:show] do
      collection do
        get :preview
      end
    end

    resources :shipments, only: [] do
      member do
        post 'init_process'
        post 'update_destination_address'
        post 'process_with_gateway'
        post 'add_order_item'
        post 'cancel_order_item'
      end
    end

    resources :invoices, only: [] do
      member do
        post 'init_process'
        post 'process_with_gateway'
      end
    end
  end

  class FullNetworks
    def self.matches?(request)
      return true if request.cookie_jar['pioneer/admin_credentials'].blank?

      pioneer_user = Pioneer::Admin.find_by(persistence_token: request.cookie_jar['pioneer/admin_credentials'].split(':')[0])
      network = pioneer_user.try(:network)
      pioneer_user && Network.all_full.include?(network)
    end
  end

  post 'categories/process_csv', to: 'categories#process_categories_csv', as: 'categories_process_csv'

  constraints FullNetworks do
    mount Sidekiq::Web => '/sidekiq'
    resources :landings, except: [:show] do
      member do
        post :update_positions
      end
    end

    resources :shops do
      member do
        resources :label, only: %i[index create]
        resource :smartcarrier, only: %i[edit update]
        get :integrate_with_meli
        post :create_products_meli
        post :delete_products_meli
      end
    end

    post 'import', to: 'shops#import'
    get 'export', to: 'shops#export'

    resources :costs
    resources :cost_imports, only: %i[create new]

    resources :brands, except: :show do
      get :destroy_relationship, as: :remove_relationship
    end

    resources :categories do
      get :delete_photo, as: :remove_photo
      collection do
        patch :update_cucarda_stores, as: :update_cucarda_stores
      end
    end

    get 'get_products_by_category/:category_id', to: 'categories#get_products_by_categories'

    resources :user_curations, only: %i[index destroy] do
      collection do
        post :commit, to: 'user_curations#commit', as: :commit
      end
    end

    resources :banners, only: %i[create destroy index new]
    put 'banners/order', to: 'banners#order'

    resources :people, as: :social_users, except: :show

    resources :guests, only: [:index]

    resources :roles do
      resource :permission, only: %i[edit update]
    end

    resources :customers, only: %i[create destroy index update edit new] do
      member do
        patch :restore
      end
    end

    scope module: 'tdd' do
      # resources :members, only: [:index, :update, :edit, :destroy]
      resources :tdd_reports, only: %i[index exports report_detail]
      get 'tdd_reports', to: 'tdd_reports#index'
      get 'tdd_reports/exports', to: 'tdd_reports#exports'
      get 'tdd_reports/report/:id', to: 'tdd_reports#show', as: :report_detail
      # resources :payments, only: [:index, :edit, :update]
      post 'payments/:id' => 'payments#generate_invoice', as: :generate_invoice
      # resources :export, only: [ :index ]
    end

    namespace 'tdd' do
      resources :members, only: %i[index update edit destroy]
      resources :payments, only: %i[index edit update] do
        resources :invoices, only: %i[new create destroy]
      end
      resources :exports, only: [:index]
      resources :imports, only: [:index]
      resources :services
      resources :tdd_reports, only: %i[index exports report_detail]
      get 'tdd_reports', to: 'tdd_reports#index'
      get 'tdd_reports/exports', to: 'tdd_reports#exports'
      get 'tdd_reports/report/:id', to: 'tdd_reports#show', as: :report_detail
      post 'payments/:id' => 'payments#generate_invoice', as: :generate_invoice

      # resources :recurrent_payments, only: [:index] do
      #  collection do
      #    post :export
      #  end
      # end

      # resources :surrender_payments, only: [:index] do
      #  collection do
      #    post :import
      #  end
      # end
    end

    namespace 'bna' do
      resources :offices
      post '/offices/new' => 'offices#create', as: :create_office
      # post '/offices/:id/edit' => 'offices#update', as: :update_office
    end

    namespace 'mimoto' do
      resources :customer_reservation_purchases, only: %i[index destroy]
    end

    resources :attempted_answers, only: %i[index destroy create new]
    post 'attempted_answers_import', to: 'attempted_answers#import'

    get 'integrations/shops',     to: 'integrations#show_shops'
    get 'integrations/products',  to: 'integrations#show_products'

    resources :products, only: %i[index show] do
      collection do
        get :export, to: 'products#export'
      end
    end
    resources :exports, only: :index do
      get :download
    end
    resources :carts, only: [:index]
    resources :products_stores do
      collection do
        post :import, to: 'products_stores#import'
        post :set_points
        post :batch_update
        post :change_order_visibility
        put :update
      end
    end
    resources :users_points do
      collection do
        get   'debit/:id' => 'users_points#debit', as: :points_debit
        post  'debit' => 'users_points#process_debit', as: :process_debit
        get   'accredit/:id' => 'users_points#accredit', as: :points_accredit
        post  'accredit' => 'users_points#process_accredit', as: :process_accredit
      end
    end

    resources :orders, only: %i[index show] do
      member do
        post :cancel, to: 'orders#cancel'
        post :refund, to: 'orders#refund'
        post :cancel_suborder, to: 'orders#cancel_suborder'
        post :refund_suborder, to: 'orders#refund_suborder'
        post :update_address, to: 'orders#update_address'
        post :update_comments, to: 'orders#update_comment'
        post :apply_state_change_suborder, to: 'orders#apply_state_change_suborder'
        post :update_status_payment, to: 'orders#update_status_payment'
        delete :delete_document_suborder, to: 'orders#delete_document_suborder'
      end
    end

    resources :sales, only: %i[index show] do
      collection do
        get :export, to: 'sales#export'
        get :tracking, to: 'sales#tracking'
        post :cancel, to: 'sales#cancel'
        post :refund, to: 'sales#refund'
      end
    end

    get 'accounting/invoices', to: 'accounting#invoices'
    get 'accounting/reports', to: 'accounting#reports'
    get 'accounting/credit_notes', to: 'accounting#credit_notes'

    resources :shipments, only: [] do
      post :process_with_gateway
    end
    resources :reviews, only: %i[create index update]
    resources :bines
    resources :payment_programs
    resources :banks, only: %i[new edit index create destroy update]
    resources :gateways, only: %i[index update]
    resources :coupons do
      collection do
        get :create_batch
        post :generate_batch
      end
    end

    resources :questions, only: [:index]
    resources :promotions
    resources :suggestions do
      member do
        post :convert_to_alias
      end
    end

    resources :admin_users

    resources :stores, except: [:show] do
      resources :carrier_stores do
        collection do
          get :duplicate
          post 'duplicate', to: 'carrier_stores#duplicator'
          post :import
          post :clean_all
          get :clean_by_shop
          post 'clean_by_shop', to: 'carrier_stores#delete_by_shop'
        end
      end
    end

    #resources :url_generator, :only => [:index] do
    #  collection do
    #    post :import
    #    post :single_url
    #  end
    #end

    get 'reports/overview', to: 'reports#overview'
    get 'reports/comments', to: 'reports#comments'

    get 'curation', to: 'curation#index', as: 'curation'
    post 'curation/commit', to: 'curation#commit', as: 'commit_curation'

    get 'content_curation', to: 'curation#index',  as: 'content_curation'

    resources :menu do
      collection do
        get :customized, to: 'menu#customized'
        put :sort,       to: 'menu#sort'
      end
    end

    resources :manufacturers, only: %i[edit index update]
    resources :sports, only: %i[edit index update]

    resources :dnis do
      collection do
        post :import
      end
    end

    resources :third_party_codes do
      collection do
        post :import
        post :new_code
        post :redemtion_code
      end
    end

    resources :vouchers, only: [:index] do
      member do
        resource :setting_coupon, only: %i[edit update]
      end
    end

    get 'email/:id', to: 'emails#show', as: :email

    resources :merchant_communications, only: %i[create new]

    resources :invoice_items, only: [:index] do
      collection do
        get :export, to: 'invoice_items#export'
        get :dashboard, to: 'invoice_items#dashboard'
      end
    end
    resources :supplier_invoices
    resources :suppliers
    resources :invoice_reports
    resources :supplier_stocks

    resources :dashboard, only: [:index]

    mount MailPreview => '/mail_view'

    resources :cucardas do
      collection do
        post :save_cucardas
        patch :update_cucarda_active
      end
    end

    resources :loyalty_configs
  end
end
