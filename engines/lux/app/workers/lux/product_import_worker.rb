require 'csv'

module Lux
  class ProductImportWorker
   include SidekiqStatus::Worker
   sidekiq_options queue: `hostname`.strip, retry: false

    def perform(product_list_path, shop_id)
      @row = 0
      @errors = []
      product_list = read_csv(product_list_path)
      product_list = product_list.delete_if { |row| row.to_hash.values.all?(&:blank?) }
    
      product_list.each do |row|
        row.each do |k, v|
          row[k] = str_sanatize(row[k]) if v.present? && v.is_a?(String)
        end
      end

      begin
        if is_update_stock?(product_list.headers, shop_id)
          import = ProductImport::Structure.new
          import.validate(product_list)

          update_product_list(product_list, import, shop_id)

          if import.error_messages.present?
            self.payload = {
                              status: 'ok',
                              error_at: 0,
                              row_with_errors: import.error_row.map { |e| e + 1 },
                              error_messages: import.error_messages
                            }
          end
        else
          products_attributes = build_attributes(product_list, shop_id)
          save_products(products_attributes, product_list) if products_attributes
        end
      rescue => e
        Rails.logger.error("Error importing products: #{e.message}")
        set_error_payload(nil, "GENERAL", e.message)
      end

    end

    private

    def update_product_list(product_list, import, shop_id)
      final_product_list = product_list.map.with_index{ |p, i| p if !import.error_row.include?(i)}.compact
      if product_list.headers.any?(/\Aimagen_\d/)
        service = AvenidaProductPictureService.new({table: product_list, shop_id: shop_id})
        service.perform
        product_list.delete(:imagen_1, :imagen_2, :imagen_3, :imagen_4, :imagen_5, :imagen_6, :imagen_7, :imagen_8)
      end
      update_stock(final_product_list)
    end

    def is_update_stock?(header, shop_id)
      [:av_sku].all? {|field| header.include? field}
    end

    def build_attributes(product_list, shop_id)
      attributes_builder = init_attributes_builder(product_list.headers, shop_id)
      set_total_work(product_list.size)
      product_list.each_with_index do |row, i|
        @row = row
        build_attributes_for_row(attributes_builder, row, i)
      end
      attributes_builder.build
    end

    def build_attributes_for_row(attributes_builder, row, index)
      at(index, "Processing row #{index}")
      attributes_builder.add(row)
      @errors << attributes_builder.errors unless attributes_builder.errors.empty?
      unless attributes_builder.valid?
        set_error_payload(row, index, attributes_builder.errors)
        Rails.logger.info("Invalid row: [#{index}]")
      end
    end

    def update_stock(variants_attributes)
      updated_variants = []
      out_of_stock_variants = {}
      errors = []
      ActiveRecord::Base.transaction do
        variants_attributes.each_with_index do |variant_attributes, index|

          variant_sku = variant_attributes[:av_sku]
          variant = Mkp::Variant.find_by_gp_sku(variant_sku)
          if variant.present?
            out_of_stock_variants[:product_id] ||= variant.product_id
            if variant.product.present? && variant_attributes[:data_name].present? && variant_attributes[:data_details].present?
              attribute = variant_attributes[:data_name]
              details = variant_attributes[:data_details]
              new_data_sheet = {}
              new_data_sheet[attribute] = [{:name => attribute, :value => details}]
              variant.product.data.merge!(new_data_sheet)
              variant.product.save
            end
            if variant_attributes[:stock].present?
              if variant_attributes[:stock].to_i.zero?
                out_of_stock_variants[:variant_ids] ||= []
                out_of_stock_variants[:variant_ids] << variant_attributes[:id].to_i
              end
              variant.update_attributes(quantity: variant_attributes[:stock])
            end
            variant.update_attributes(sku: variant_attributes[:sku]) if variant_attributes[:sku].present?
            variant.update_attributes(ean_code: variant_attributes[:ean]) if variant_attributes[:ean].present?
            variant.product.solr_index
            variant.product.set_approved_for_stores(true)
            updated_variants << variant
          elsif (product = Mkp::Product.find_by_id(variant_sku)).present?
            fields = [:title, :description, :sale_price, :sale_price_without_taxes, :iva, :origin_of_product, :brand, :energy_efficiency]
            product_attributes = {}.tap do |hash|
              fields.each do |field|
                variant_attributes[field] && hash[field] = variant_attributes[field]
              end
              variant_attributes[:manufacturer] && to_manufacturer_id(variant_attributes[:manufacturer]) && hash[:manufacturer_id] = @manufacturer
              variant_attributes[:category] && to_category_id(variant_attributes[:category]) && hash[:category_id] = @category
              variant_attributes[:price] && hash[:regular_price] = variant_attributes[:price]
              variant_attributes[:price_without_taxes] && hash[:regular_price_without_taxes] = variant_attributes[:price_without_taxes]
              variant_attributes[:origin_of_product] && hash[:origin_of_product] = variant_attributes[:origin_of_product].to_i
              variant_attributes[:brand] && hash[:brand] = variant_attributes[:brand]
              variant_attributes[:energy_efficiency] && hash[:energy_efficiency] = variant_attributes[:energy_efficiency]

              variant_attributes[:sale_on] && hash[:sale_on] = to_date(variant_attributes[:sale_on])
              variant_attributes[:available_on] && hash[:available_on] = to_date(variant_attributes[:available_on])
              variant_attributes[:sale_until] && hash[:sale_until] = to_date(variant_attributes[:sale_until])
              variant_attributes[:pickeable] && hash[:pickeable_status] = variant_attributes[:pickeable] if pickeable_valid?(variant_attributes[:pickeable])
              hash[:packages_attributes] = Import::GenerationPackages.new(variant_attributes).build if packages_builds?(variant_attributes)
            end
            product.packages.delete_all if product_attributes[:packages_attributes].present?
            product.update_attributes(product_attributes)
            if product.errors.any?
              errors << "| Product id #{product.id}: #{product.errors.full_messages}"
            end
            product.solr_index
            product.recreate_variants_visibility
            product.set_approved_for_stores(true)
          else
            variants_attributes
            Rails.logger.info("Invalid row: [#{index}]")
          end
        end
      end

      if out_of_stock_variants.present?
        Mkp::Integration::RemoteSyncWorker.perform_async(
          out_of_stock_variants[:product_id],
          out_of_stock_variants[:variant_ids]
        )
      end

      if errors.present?
        message = "[IMPORTACION - PRODUCTOS] No se realizo la importacion de los siguientes productos #{errors.join(' - ')}"
        set_error_payload(nil, 'GENERAL', message)
      else
        self.payload = {
          status: 'successful',
          products: updated_variants.map(&:product).uniq.size,
          variants: updated_variants.size,
          strategy: 'update'
        }
      end
    end

    def packages_builds?(row)
      %i[packages width height weight length].all?{ |attr| row[attr].present? }
    end

    def to_manufacturer_id(manufacturer_id)
      (@manufacturer = Mkp::Manufacturer.find_by_name(manufacturer_id).try(:id)).present?
    end

    def to_category_id(category)
      if (id = category.to_i) > 0
        @category = Mkp::Category.find_by_id(id).try(:id)
      else
        name = category
        @category = Mkp::Category.active.find_by_name(name).try(:id)
      end
      @category.present?
    end

    def save_products(products_attributes, product_list)
      ActiveRecord::Base.transaction do
        product_errors = []
        products_attributes.each do |product_attributes|
          begin
            external_sku = product_attributes[:variants_attributes][0][:sku]
            variants_attributes = product_attributes.delete(:variants_attributes)
            product = Product.create!(product_attributes)
            create_variants(variants_attributes, product.id)
            product.solr_index
            product.set_approved_for_stores(true)
          rescue
            product_errors << external_sku
            next
          end

        end
        message = "[IMPORTACION - PRODUCTOS] correspondiente al shop #{@shop.title} (#{@shop.id}),
        no se realizó la importación de los siguientes productos ||
        external_sku: #{product_errors} - #{@errors}"
        RocketChatNotifier.notify message, webhook_dest: :platform if product_errors.present?
      end

      self.payload = { status: 'successful',
                       products: products_attributes.size,
                       variants: product_list.size,
                       strategy: 'update' }
    end

    def create_variants(variants_attributes, product_id)
      skus = []
      variants = variants_attributes.map do |variant_attributes|
        variant = Mkp::Variant.new(variant_attributes.merge(product_id: product_id))
        variant.send(:build_gp_sku, skus)
        skus << variant.gp_sku
        variant.save!
        variant
      end
      variants.first.product.recreate_variants_visibility if variants.present?
    end

    def update_variants(variants_attributes)
      variants_attributes.each do |variant_attributes|
        variant = Mkp::Variant.find(variant_attributes.fetch(:id))
        variant.assign_attributes(variant_attributes.except(:id))
        variant.save! if variant.changed?
        variant.product.recreate_variants_visibility
      end
    end

    def init_attributes_builder(headers, shop_id)
      @shop = Shop.find(shop_id)
      mapper = ProductImport::Mapper.new(headers, @shop)
      ProductImport::AttributesBuilder.new(mapper, @shop)
    end

    def read_csv(path)
      CSV::HeaderConverters.merge!({strip: lambda { |h| h.strip }})
      options = { headers: :first_row, header_converters: [:downcase, :strip, :symbol], skip_blanks: true }
      CSV.open(path, options).read
    rescue ArgumentError
      options[:encoding] = 'ISO8859-1'
      CSV.open(path, options).read
    end

    def set_total_work(row_count)
      self.total = row_count
    end

    def set_error_payload(row, index, errors)
      self.payload = {
            status: 'errors',
            error_at: index,
            row_with_errors: row.to_a,
            error_messages: { general: ["Hemos tenido un problema, por favor comuníquese con su asesor comercial via mail adjuntando el <NAME_EMAIL>.  Error: #{index} 'Importación Fallida.' ERROR: #{errors.to_s}"] }
          }
    end

    def str_sanatize(field)
      field = field.strip
      field[0] = '' if field[0].slice('|').present?
      field
    end

   def to_date(date)
     Time.zone.local_to_utc(DateTime.strptime(date, '%Y-%m-%d'))
   rescue
     Date.today
   end

   def pickeable_valid?(status)
     status&.match(/^(\d)+$/).present? && ((0..2).cover? status.to_i)
   end
  end
end
