puts "Iniciando desde #{Dir.pwd}"
unless ENV['RAILS_ENV']
  puts "Es necesario setear la variable RAILS_ENV"
  exit 
end

God.watch do |w|
  w.name = "Puma"
  w.start = "bundle exec puma -e #{ENV['RAILS_ENV']} -C config/puma.rb  -p 3000"
  w.group = "puma"
  w.log = "#{Dir.pwd}/log/console.log"
  w.err_log = "#{Dir.pwd}/log/console_err.log"
  w.dir = "#{Dir.pwd}"
  w.keepalive
end

num_sidekiqs = ENV.fetch('NUM_SIDEKIQ', 5).to_i

num_sidekiqs.times do |num|
  God.watch do |w|
    w.name = "Sidekiq #{num}"
    w.start = "bundle exec sidekiq -e #{ENV['RAILS_ENV']} -C config/sidekiq.yml"
    w.group = "sidekiq"
    w.log = "#{Dir.pwd}/log/sidekiq.log"
    w.err_log = "#{Dir.pwd}/log/sidekiq_err.log"
    w.dir = "#{Dir.pwd}"
    w.keepalive
  end
end