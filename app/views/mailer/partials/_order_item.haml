- is_admin ||= false
- hide_purchase_date = hide_purchase_date.present? ? hide_purchase_date : false
- variant = item.variant
- product = variant.product
- variant_url = item.suborder.store.hostname + variant.get_url
- properties = Mkp::Product::AVAILABLE_PROPERTIES

- if product.warranty?
  = render partial: 'mailer/partials/order_item_warranty', locals: { item: item, is_admin: is_admin, hide_purchase_date: hide_purchase_date }
- else
  %table
    %tbody
      %tr.border-bottom{:style => ""}
        %td{width: '40%', :style => "text-align: center;"}
          = link_to (image_tag variant.picture.url(:tm), size: '198x200'), variant_url if variant.picture
        %td
          %h4= t('.summary').upcase

          %p
            = t('.description')
            %br
            %p{:style => "line-height: 19px;"}
              %strong
                %a{ href: variant_url }= product.title
              %br
              = variant.description.truncate(100)

          %p{:style => "line-height: 17px;"}
            - unless hide_purchase_date
              = t('.date')
              %strong= item.created_at.strftime("%d/%m/%y - %H:%M:%S")

            - unless product.has_no_property?
              - variant.properties.keys.each do |property|
                %br
                  =t('.' + property.to_s)
                  - if property.to_sym == :color
                    %strong= variant.color_name
                  - else
                    %strong= variant.properties[property]

            = t('.quantity')
            %strong= item.quantity
            %br
              = t('.price')
              %strong.currency= number_to_currency(item.unit_price_charged, unit: current_currency_format)
            - if item.points > 0
              = t('.points')
              %strong.currency= number_with_precision(item.points, precision: 0)
              %br
              = t('.points_money')
              %strong.currency= number_to_currency(item.point_equivalent_with_iva * item.points, unit: current_currency_format)
            - payments = item.suborder.order.payments.where.not(gateway: ['VisaPuntos', 'SystemPoint', 'LoyaltyBna'])
            - if payments.any?
              %br
              = t('.payment_in_tc')
              %strong.currency= number_to_currency(item.mail_item_total, unit: current_currency_format)
              - installments = payments.first.number_of_installments(payments.first)
              - if installments.present?
                %br
                = t('.installments')
                %strong= installments
            %br
              = t('.order_id')
              %strong= item.suborder.public_id

          - if @current_store.name == 'bna'
            %h4{:style => "margin-bottom: 0px;"}
              = "Datos del vendedor"

            %small
              = "Ante cualquier consulta que tengas sobre la facturación, el producto adquirido o el envió, puedes ponerte en contacto con el Vendedor"

            %p{:style => "line-height: 17px;"}
              = "Vendedor #{item.suborder.shop.public_name}"
              - if item.suborder.shop.phone != ""
                %br
                = "Teléfono #{item.suborder.shop.phone}"

              - if item.suborder.shop.public_email != ""
                %br
                = "Email "
                %a{:href => "mailto:#{item.suborder.shop.public_email}"}
                  = "#{item.suborder.shop.public_email}"

              - if item.suborder.shop.web != ""
                %br
                = "Web #{item.suborder.shop.web}"
          
          - if @current_store.name == 'bancociudad'
            %p{ :style => "font-size: 1.5em; margin: 20px 0 0 0" }
              %strong= t('.seller_details').upcase

            %p
              %strong= t('.seller_info_channels')

            - if item.suborder.shop.public_name.present?
              %p
                = t('.seller_public_name')
                %strong= item.suborder.shop.public_name

            - if item.suborder.shop.public_email.present?
              %p
                = t('.seller_public_email')
                %strong= item.suborder.shop.public_email

            - if item.suborder.shop.phone.present?
              %p
                = t('.seller_public_phone')
                %strong= item.suborder.shop.phone

            - if item.suborder.shop.web.present?
              %p
                = t('.seller_website')
                %strong= item.suborder.shop.web
