module Mkp
  class Shop < ActiveRecord::Base
    has_paper_trail
    extend FriendlyId
    include Concerns::HasSlugOnRedis
    include ::Concerns::Cucardas

    friendly_id :title, use: [:slugged, :scoped], scope: :network

    belongs_to :fulfillment_shop, class_name: 'Shop'
    belongs_to :office, class_name: 'Mkp::Bna::Office'
    belongs_to :cucarda, class_name: 'Cucarda'

    has_many :exports, class_name: 'Mkp::Export'
    has_many :admins, class_name: 'User', through: :shop_admins
    has_many :banners, class_name: 'Banner::Shop'
    has_many :coupons, class_name: 'Coupon::Shop'
    has_many :integrations, class_name: 'Integration::Base', dependent: :destroy
    has_many :products, class_name: 'Product', dependent: :destroy
    has_many :questions, class_name: 'Question', through: :products
    has_many :shipping_methods, class_name: 'ShippingMethod', through: :zones
    has_many :shop_admins, dependent: :destroy
    has_many :suborders, class_name: 'Suborder'
    has_many :variants, dependent: :destroy
    has_many :warehouses, class_name: '::Mkp::Warehouse', as: :addressable
    has_many :zones, class_name: 'Zone'
    has_many :shop_stores, dependent: :destroy
    has_many :stores, through: :shop_stores
    has_many :first_data_credentials, dependent: :delete_all
    # has_many :mkp_cart_delivery_options, :class_name => 'Mkp::CartDeliveryOption'

    has_many :payment_credentials, dependent: :destroy, foreign_key: :shop_id

    has_one :setting, class_name: 'Mkp::ShopSetting', foreign_key: 'mkp_shop_id', dependent: :destroy

    validate :requirements_before_visible
    validate :decidir_site_id_blank
    validate :decidir_site_id_format
    validates :title, presence: true
    validates :visa_puntos_equivalence, numericality: { greater_than: 0 }, allow_nil: true
    validates :account_number,
              format: { with: /\A\d+\z/, message: 'solo puede contener números' },
              length: { in: 5..20 },
              allow_blank: true

    after_create :set_default_fulfillment_shop
    after_create :assign_setting
    after_create :generate_mercadolibre_redirect
    after_create :delete_old_carriers

    before_update :update_cucarda_id_for_shop
    after_update :products_refresh

    before_save :sync_account_number_to_related_shops, if: :account_number_changed?

    scope :by_network, ->(network) { where(mkp_shops: { network: network }) if network.present? }
    scope :visible, -> { where('mkp_shops.visible = ?', true) }

    delegate :notify_purchases,
             :notify_purchases_emails,
             :sale_commission, :monthly_fee, :customs_signer_name, to: :setting

    default_scope { where(deleted_at: nil) }

    accepts_nested_attributes_for :setting
    accepts_nested_attributes_for :warehouses
    accepts_nested_attributes_for :shop_stores
    accepts_nested_attributes_for :first_data_credentials, reject_if: :all_blank, allow_destroy: true
    accepts_nested_attributes_for :payment_credentials, allow_destroy: true, update_only: true

    BNA_MIMOTO_ID = 43.freeze
    def name
      title
    end

    def charge_tax_to?(state)
      return false unless Network[network].charges_taxes?
      return false if tax_rates[state].blank?
      (!!Float(tax_rates[state][:rate])) rescue false
    end

    def assign_merchant(user_id)
      user = User.find(user_id)
      return nil if user.is_a?(Brand) && user.shops.any?

      user.update_column(:roles_mask, 3)
      user.shops << self unless user.shops.exists?(self.id)
      user
    end

    def brand
      shop_admins.where('mkp_shop_admins.owner' => true).first.try(:admin)
    end

    def assign_owner(user_id)
      user = User.find(user_id)
      shop_owner = shop_admins.where('mkp_shop_admins.owner = ?', true).first
      is_owner = shop_owner.present? && shop_owner.admin_id == user.id
      return user if is_owner

      revoke_owner(shop_owner.admin_id) if shop_owner.present?
      assign_merchant(user.id)

      shop_owner = shop_admins.where('mkp_shop_admins.admin_id = ?', user.id)
                              .first
      shop_owner.update_column(:owner, true)
      user
    end

    def revoke_merchant(user_id)
      user = User.find user_id
      return user unless user.shops.exists?(self)
      merchants.delete(user)
      user
    end

    def revoke_owner(user_id)
      user = User.find user_id

      shop_owner = shop_admins.where('mkp_shop_admins.admin_id = ?', user.id)
                              .where('mkp_shop_admins.owner = ?', true)
                              .first

      if shop_owner.present? && shop_owner.admin_id == user.id
        shop_owner.owner = false
        shop_owner.save
      end

      user
    end

    def currency
      Currency.find_by_network(network)
    end

    def merchants
      admins
    end

    def can_be_managed_by?(user)
      admins.include?(user)
    end

    def owned_by?(user)
      owner = admins.where('mkp_shop_admins.owner = ?', true).first
      owner && owner == user
    end

    def managers
      admins.where('mkp_shop_admins.owner = ?', false)
    end

    def owner
      admins.where('mkp_shop_admins.owner = ?', true).first
    end

    # this replaces the max_length option on friendy_id v.3
    def normalize_friendly_id(string)
      super(string.parameterize.mb_chars.limit(50).to_s)
    end

    def should_generate_new_friendly_id?
      return false if changes.key?(:slug) || !changes.key?(:title)
      super
    end

    def warehouse
      warehouses.first
    end

    def pickups
      warehouses.for_pickup
    end

    def goodpeople?
      id == 1
    end

    def shipping_method_or_address_destroyed
      hide_if_cannot_ship_products
    end

    def fulfillment_by_other?
      fulfillment_shop_id != id
    end

    def fulfilled_by_gp?
      return false unless Network[network].shops_can_be_fulfilled?
      fulfillment_shop_id == Network[network].default_shop_id
    end

    def us_network?
      network == 'US'
    end

    def tax_rates
      setting.tax_rates
    end

    def tax_rate_for_state(state)
      tax_rates[state][:rate].to_f rescue 0.0
    end

    def authorization_token
      Digest::MD5.hexdigest(id.to_s+'-'+created_at.to_s)
    end

    def job_queued_key(task_name)
      "working:#{task_name}:job:queued:shop/#{id}"
    end

    def destroy
      return super if suborders.count == 0
      update_attributes(deleted_at: Time.now)
      products.update_all(deleted_at: Time.now)
      variants.update_all(deleted_at: Time.now)
    end

    def first_data_credential_for(items)
      credentials = []
      items.each do |hash|
        coll = first_data_credentials.select {|each| !each.categories_ids.blank? && !(each.categories_ids & ((Mkp::Variant.find hash[:id]).categories.collect(&:id))).blank?}
        if coll.blank?
          credentials << first_data_credentials.select {|each| each.categories_ids.blank?}
        else
          credentials << coll
        end
      end
      credentials.flatten.sort_by(&:priority).first
    end

    def first_data_credential_for_order(order)
      credentials = []
      order.items.each do |item|
        if item.product.shop == self
          coll = first_data_credentials.select {|each| !each.categories_ids.blank? && !(each.categories_ids & (item.variant.categories.collect(&:id))).blank?}
          if coll.blank?
            credentials << first_data_credentials.select {|each| each.categories_ids.blank?}
          else
            credentials << coll
          end
        end
      end
      credentials.flatten.sort_by(&:priority).first
    end

    def get_decidir_site_id(store)
      return decidir_site_id if decidir_site_id.present?

      store.decidir_credentials.present? ? store.decidir_credentials.avenida_distributed_site_id : AVENIDA_JR_SITE_ID
    end

    def payment_program_for(store)
      shop_store = shop_stores.where(store: store).first
      shop_store.payment_program if shop_store
    end

    def modo_payment_program_for(store)
      shop_store = shop_stores.where(store: store).first
      shop_store&.bin
    end

    def refund_with_selectable_items?
      shipment_strategy == 'product'
    end

    def is_refundable?(suborder)
      case shipment_strategy
      when 'product'
        suborder.is_refundable_by_product?
      else
        suborder.is_refundable_by_suborder?
      end
    end

    def store_included?(store_id)
      store = Mkp::Store.find(store_id)
      stores.include?(store)
    end

    def update_bna_offices
      return unless needs_office_update

      office_user_ids = Mkp::Bna::Office.where.not(user: nil).pluck(:user_id)
      return if office_user_ids.empty?

      existing_user_ids = Mkp::ShopAdmin.where(shop_id: id, admin_id: office_user_ids).pluck(:admin_id)

      users_to_update = office_user_ids - existing_user_ids
      return if users_to_update.empty?

      users_to_update.each do |user_id|
        Mkp::ShopAdmin.create(shop_id: id, admin_id: user_id, owner: false)
      end
    end

    def products_refresh
      return unless self.changed?
      no_needed_to_solr_update = [
        "updated_at",
        "decidir_site_id",
        "decidir_percentage",
        "visa_puntos_equivalence",
        "decidir_public_key",
        "decidir_private_key",
        "mercadolibre_app_id",
        "mercadolibre_secret_key",
        "mercadolibre_redirect_uri",
        "cuit",
        "external_shop_id"
      ]

      return unless (self.changed - no_needed_to_solr_update).present?

      if products.count > 50
        message = "🚀 [SHOPADMIN] Shop #{id} tiene #{products.count} productos, usando worker asíncrono"
        Rails.logger.info message
        RocketChatNotifier.notify(message, webhook_dest: :platform)

        ShopProductsRefreshWorker.perform_async(id)
      else
        message = "🚀 [SHOPADMIN] Shop #{id} tiene #{products.count} productos, ejecutando sincrónicamente"
        Rails.logger.info message
        RocketChatNotifier.notify(message, webhook_dest: :platform)

        products_refresh_optimized
      end
    end

    def products_refresh_optimized
      start_time = Time.current
      Rails.logger.info "🔧 [SHOPADMIN] Iniciando products_refresh_optimized para shop #{id}"

      product_ids = products.pluck(:id)
      Rails.logger.info "🔧 [SHOPADMIN] Obtenidos #{product_ids.size} product_ids"

      variants_quantities = Mkp::Variant
        .where(product_id: product_ids)
        .where('mkp_variants.deleted_at IS NULL')
        .group(:product_id)
        .sum(:quantity)

      sold_counts = Mkp::OrderItem
        .where(product_id: product_ids)
        .group(:product_id)
        .sum(:quantity)

      last_sold_ats = Mkp::OrderItem
        .where(product_id: product_ids)
        .group(:product_id)
        .maximum(:created_at)

      rejected_store_ids = Mkp::ProductStore
        .where(product_id: product_ids, status: 'rejected')
        .group(:product_id)
        .pluck(:product_id, :store_id)
        .group_by(&:first)
        .transform_values { |pairs| pairs.map(&:last) }

      pending_store_ids = Mkp::ProductStore
        .where(product_id: product_ids, status: 'pending')
        .group(:product_id)
        .pluck(:product_id, :store_id)
        .group_by(&:first)
        .transform_values { |pairs| pairs.map(&:last) }

      approved_store_ids = Mkp::ProductStore
        .where(product_id: product_ids, status: 'approved')
        .group(:product_id)
        .pluck(:product_id, :store_id)
        .group_by(&:first)
        .transform_values { |pairs| pairs.map(&:last) }

      precalculated_values = {
        variants_quantities: variants_quantities,
        sold_counts: sold_counts,
        last_sold_ats: last_sold_ats,
        rejected_store_ids: rejected_store_ids,
        pending_store_ids: pending_store_ids,
        approved_store_ids: approved_store_ids
      }

      Mkp::Product.set_precalculated_values(precalculated_values)
      Rails.logger.info "🔧 [SHOPADMIN] Valores precalculados establecidos"

      optimized_products = products.includes(
        :category,
        :manufacturer,
        :voucher,
        :pictures,
        category: { category_stores: :store }
      )
      Rails.logger.info "🔧 [SHOPADMIN] Cargados #{optimized_products.size} productos con includes"

      optimized_variants = variants.includes(
        :product,
        :external_objects,
        product: [:category, :manufacturer, :shop]
      )
      Rails.logger.info "🔧 [SHOPADMIN] Cargados #{optimized_variants.size} variants con includes"

      Rails.logger.info "🔧 [SHOPADMIN] Iniciando indexación de productos..."
      Sunspot.remove(Mkp::Product) { with :shop_id, self.to_s }
      Sunspot.index optimized_products
      Sunspot.commit
      Rails.logger.info "🔧 [SHOPADMIN] ✅ Productos indexados y commiteados"

      Rails.logger.info "🔧 [SHOPADMIN] Iniciando indexación de variants..."
      Sunspot.remove(Mkp::Variant) { with :shop_id, self.to_s }
      Sunspot.index optimized_variants
      Sunspot.commit
      Rails.logger.info "🔧 [SHOPADMIN] ✅ Variants indexados y commiteados"

      Mkp::Product.clear_precalculated_values

      duration = (Time.current - start_time).round(2)
      completion_message = "✅ [SHOPADMIN] Shop #{id}, sus productos y variantes reindexadas, terminado en #{duration}s"
      Rails.logger.info completion_message

      RocketChatNotifier.notify(completion_message, webhook_dest: :platform)
    end

    protected

    def carrier_stores
      shop_stores.active.includes(:store)
    end

    def delete_old_carriers
      carrier_stores.destroy_all
    end

    def requirements_before_visible
      if visible && !can_ship_products?
        errors.add(:visible, I18n.t('lux.accounts.show.before_going_visible'))
      end
    end

    def has_pickup?
      warehouses.any?(&:pickup)
    end

    private

    def can_ship_products?
      (fulfillment_shop && fulfillment_shop.warehouses.any?) || !delivery_by_matrix
    end

    def hide_if_cannot_ship_products
      update_column(:visible, false) unless can_ship_products?
    end

    def set_default_fulfillment_shop
      update_column(:fulfillment_shop_id, id) if fulfillment_shop.nil?
    end

    def assign_setting
      setting = create_setting
      setting.set_default_taxes if us_network?
    end

    def generate_mercadolibre_redirect
      update_column(:mercadolibre_redirect_uri, MELI['callback_url'] + "?shop_id=#{id}")
    end

    def needs_office_update
      stores.present? && stores.map(&:id).include?(BNA_MIMOTO_ID)
    end

    def decidir_site_id_blank
      bna_store = self.stores.include?(Mkp::Store.find(41))
      errors.add(:decidir_site_id, "El Site ID no puede estar en blanco") if self.decidir_site_id.blank? && bna_store
    end

    def decidir_site_id_format
      return if decidir_site_id.nil?

      site_id_format = self.decidir_site_id =~ /^\d{0,8}$/
      errors.add(:decidir_site_id, "Solo caracteres numericos, de 0 a 9, sin espacios, hasta 8 caracteres") if not site_id_format
    end

    def sync_account_number_to_related_shops
      return if cuit.blank?

      related_shops = Mkp::Shop.where.not(id: id)
                               .joins(:shop_stores)
                               .where(cuit: cuit)
                               .where('mkp_shop_stores.store_id = ?', 41)
                               .distinct

      # rubocop:disable Rails/SkipsModelValidations
      related_shops.update_all(account_number: account_number)
      # rubocop:enable Rails/SkipsModelValidations
    end
  end
end
