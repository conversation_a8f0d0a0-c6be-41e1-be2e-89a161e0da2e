class MailPreview < MailView
  # Social
  def welcome
    UserMailer.welcome_email(User.last, request.params['locale'])
  end

  def enter_contest
    PromotionMailer.entered_contest(Brand.last, User.last)
  end

  def password_reset
    UserMailer.password_reset_email(User.last)
  end

  def partial_signup_email
    UserMailer.partial_signup_email(PartialSignup.last)
  end

  # Brands
  def brand_partial_signup_email
    UserMailer.brand_partial_signup_email(PartialSignup.last)
  end

  def brand_application_form_email
    UserMailer.brand_application_form_email({ company_name: Brand.last.name },
                                            request.params['locale'])
  end

  def brand_application_form_admin_email
    brand = Brand.last
    brand_data = { company_name: brand.name,
                   email: brand.email,
                   website: brand.website_url }
    UserMailer.brand_application_form_admin_email(brand_data,
                                                  request.params['locale'])
  end

  # Mkp
  def question_user
    Mkp::ShopMailer.question_user(last_question_by_network)
  end

  def question_shop
    question = Mkp::Question.includes([product: [:shop]])
                            .where('mkp_shops.network = ?', request.params['network'])
                            .last ||
               Mkp::Question.includes([product: [:shop]])
                            .last

    Mkp::ShopMailer.question_shop(question)
  end

  def answer_shop
    Mkp::ShopMailer.answer_shop(last_question_by_network)
  end

  def report_to_admin
    Mkp::ReportMailer.network_admin_notification(last_reported_review_by_network, network_admin)
  end

  def report_to_shop_admin
    Mkp::ReportMailer.shop_admin_notification(last_reported_review_by_network,
                                              User.last)
  end

  # Checkout
  def customer_purchase_cancellation_notification
    Mkp::OrderMailer.customer_purchase_cancellation_notification(last_order_by_network, nil, nil, 'v5/mailer/bna/purchase_cancellation')
  end

  def customer_purchase_confirmation
    Mkp::OrderMailer.customer_purchase_confirmation(last_order_by_network, nil)
  end

  def customer_purchase_confirmation_bna
    Mkp::OrderMailer.customer_purchase_confirmation(Mkp::Store.find(41).orders.last, 'v5/mailer/bna/purchase_confirmation')
  end

  def customer_paid_confirmation
    Mkp::OrderMailer.customer_paid_confirmation(last_order_by_network)
  end

  def shop_fulfillment_notification
    Mkp::OrderMailer.shop_fulfillment_notification([last_suborder_by_network],
                                                   User.last)
  end

  def network_admin_paid_confirmation
    Mkp::OrderMailer.network_admin_paid_confirmation(last_order_by_network,
                                                     network_admin)
  end

  def network_admin_purchase_confirmation
    Mkp::OrderMailer.network_admin_purchase_confirmation(last_order_by_network,
                                                         network_admin)
  end

  def shop_paid_confirmation
    Mkp::OrderMailer.shop_paid_confirmation(last_suborder_by_network, User.last)
  end

  # Shipments
  def notify_shipped_to_user
    shipment = Mkp::Shipment.includes([suborders: [:shop]])
                          .where('mkp_shops.network = ?', request.params['network']).last ||
               Mkp::Shipment.includes([suborders: [:shop]])
    Mkp::ShipmentMailer.notify_shipped_to_user(shipment)
  end

  def notify_pick_up_to_user
    shipment = Mkp::Shipment.includes([suborders: [:shop]])
                          .where('mkp_shops.network = ?', request.params['network']).last ||
               Mkp::Shipment.includes([suborders: [:shop]])
    Mkp::ShipmentMailer.notify_pick_up_to_user(shipment)
  end

  def notify_pick_up_delivered_to_user
    shipment = Mkp::Shipment.includes([suborders: [:shop]])
                            .where('mkp_shops.network = ?', request.params['network']).last ||
               Mkp::Shipment.includes([suborders: [:shop]])
    Mkp::ShipmentMailer.notify_pick_up_delivered_to_user(shipment)
  end

  def notify_delivered_to_customer
    shipment = last_order.shipments[0]
    Mkp::ShipmentMailer.notify_delivered_to_customer(shipment)
  end

  def no_shipping_options_feedback
    FeedbackMailer.checkout(last_feedback_by_network, network_admin)
  end

  ## System Communications
  # Left Carts
  def left_cart_items
    SystemCommunication::LeftCartMailer.with_items_only \
      last_left_cart_communication('initialized')
  end

  def left_cart_address
    SystemCommunication::LeftCartMailer.selecting_address \
      last_left_cart_communication('shipping_address')
  end

  def left_cart_payment
    SystemCommunication::LeftCartMailer.selecting_payment \
      last_left_cart_communication('payment')
  end

  def left_cart_review
    SystemCommunication::LeftCartMailer.reviewing \
      last_left_cart_communication('review')
  end

  #Abandoned carts
  def notify_customer
    Mkp::AbandonedCartMailer.notify_customer(Mkp::Cart.last)
  end

  # Review Feedback
  def review_communication
    SystemCommunication::ReviewMailer.feedback last_review_communication
  end

  def review_thanks_communication
    SystemCommunication::ReviewMailer.thanks last_review_thanks_communication
  end

  # BNA Mailers

  def last_order
    Mkp::Order.last
  end

  def bna_mailer_notify_order_booked
    Mkp::Bna::CustomerMailer.notify_order_booked(last_order)
    #Mkp::Bna::ProviderMailer.notify_order_booked(last_order)
    #Mkp::Bna::StoreMailer.notify_order_booked(last_order, '0005')
  end

  def bna_mailer_notify_order_approved
    Mkp::Bna::CustomerMailer.notify_order_approved(last_order)
    #Mkp::Bna::ProviderMailer.notify_order_approved(last_order)
  end

  def bna_mailer_notify_order_billed
    Mkp::Bna::StoreMailer.notify_order_billed(last_order, '0005')
  end

  def bna_mailer_notify_order_posted
    Mkp::Bna::ProviderMailer.notify_order_posted(last_order)
  end

  def bna_mailer_notify_order_delivered
    #Mkp::Bna::CustomerMailer.notify_order_delivered(last_order)
    Mkp::Bna::StoreMailer.notify_order_delivered(last_order, '0005')
  end

  def bna_mailer_notify_order_cancelled
    Mkp::Bna::ProviderMailer.notify_order_cancelled(last_order)
  end

  def bna_mailer_notify_order_declined
    Mkp::Bna::ProviderMailer.notify_order_declined(last_order)
  end

  def bna_mailer_notify_order_pending_cancellation
    Mkp::Bna::StoreMailer.notify_order_pending_cancellation(last_order, '0005')
  end

  protected

  def last_reported_review_by_network
    Mkp::Review.includes(product: [:shop]).where('mkp_shops.network',
                                                 request.params['network']).order('RAND()').last ||
      Mkp::Review.last
  end

  def get_last_notification_for(subclass, _locale)
    "Notification::#{subclass}".constantize
                               .includes(:user)
                               .where('users.network = ?', request.params['network'])
                               .order('RAND()').last ||
      "Notification::#{subclass}".constantize.includes(:user).last
  end

  def last_question_by_network
    Mkp::Question.includes(:user)
                 .where('users.network = ?', request.params['network'])
                 .order('RAND()')
                 .last || Mkp::Question.includes(:user).last
  end

  def last_order_by_network
    Mkp::Order.find(583292)
    # Mkp::Order.where(network: request.params['network']).order('RAND()').last ||
    # Mkp::Order.last
  end

  def last_suborder_by_network
    last_order_by_network.suborders.first
  end

  def last_feedback_by_network
    Feedback::Checkout.where('network = ?',
                             request.params['network'])
                      .order('RAND()').last ||
      Feedback::Checkout.last
  end

  def last_review_communication
    SystemCommunication::Review.where('network = ?',
                                      request.params['network'])
                               .order('RAND()').last || SystemCommunication::Review.last
  end

  def last_review_thanks_communication
    SystemCommunication::ReviewThanks.where('network = ?',
                                            request.params['network'])
                                     .order('RAND()').last || SystemCommunication::ReviewThanks.last
  end

  def last_left_cart_communication(status)
    where_clause = { network: request.params['network'] }
    SystemCommunication::LeftCart
      .where('network = :network', where_clause)
      .where('data LIKE :data', { data: "%cart_status: #{status}%" }).last ||
      SystemCommunication::LeftCart
        .where('network = ?', where_clause).last ||
      SystemCommunication::LeftCart.last
  end

  def network_admin
    Pioneer::Admin.by_network(request.params['network']).order('RAND()').last ||
      Pioneer::Admin.last
  end
end
